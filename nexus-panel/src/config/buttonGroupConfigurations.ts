// 按钮组配置数据模块
import { ButtonStyleType } from "../components/SidebarButtonStyles";
import type { ButtonConfig, FormFieldConfig, RenderOrderItem } from "../components/SidebarButtonGroup";
import type { GroupedConfigurationData, ProcessedConfigurationItem } from "../services/api/types";
// 导入类型定义以支持 window.ultimatePKControls
import { PKStage } from "../types/ultimatePK";
// 导入争分夺秒工具函数
import { parseTimeRaceContentType, isTimeRaceContentType, getTimeRaceDisplayName } from "../utils/timeRaceUtils";
// 导入全局计时器管理器
import { globalTimerManager } from "../utils/globalTimerManager";
// 导入计时器样式
import "../components/TimeRaceTimer.css";
// 导入 navigationApi 服务
import { NavigationApiService } from "../services/api/navigationApi";



/**
 * 确认对话框配置接口
 */
export interface ConfirmationDialogConfig {
	/** 对话框标题 */
	title: string;
	/** 对话框内容模板，{targetLabel} 将被替换为目标选项的标签 */
	content: string;
	/** 确认按钮文本，默认为"确认" */
	confirmLabel?: string;
	/** 取消按钮文本，默认为"取消" */
	cancelLabel?: string;
	/** 对话框变体，默认为"confirmation" */
	variant?: 'confirmation' | 'information' | 'warning' | 'error' | 'destructive';
}

/**
 * ActionGroup 配置项接口
 */
export interface ActionGroupItem {
	key: string;
	label: string;
}

/**
 * ActionGroup 按钮组配置接口
 */
export interface ActionGroupButtonConfig {
	type: 'actionGroup';
	title: string;
	tooltipContent: string;
	selectionMode: 'single';
	disallowEmptySelection: true;
	defaultSelectedKeys: string[];
	items: ActionGroupItem[];
	onSelectionChange: (key: string) => void;
	/** 可选的确认对话框配置，如果提供则在选择变更前显示确认对话框 */
	confirmationDialog?: ConfirmationDialogConfig;
}

/**
 * 传统按钮组配置接口
 */
export interface TraditionalButtonGroupConfig {
	type?: 'traditional';
	title: string;
	tooltipContent: string;
	buttons: ButtonConfig[];
	formFields?: FormFieldConfig[];
	additionalButtons?: ButtonConfig[];
	renderOrder?: RenderOrderItem[];
	showBreakBeforeButtons?: boolean;
	showBreakBeforeAdditionalButtons?: boolean;
}

/**
 * 统一按钮组配置接口（支持传统按钮和ActionGroup）
 */
export type ButtonGroupConfig = TraditionalButtonGroupConfig | ActionGroupButtonConfig;

/**
 * 传统按钮组配置工厂函数
 * @param config 传统按钮组配置
 * @returns 完整的传统按钮组配置
 */
export const createButtonGroupConfig = (
	config: TraditionalButtonGroupConfig,
): TraditionalButtonGroupConfig => ({
	formFields: [],
	additionalButtons: [],
	renderOrder: undefined,
	showBreakBeforeButtons: undefined,
	showBreakBeforeAdditionalButtons: undefined,
	...config,
});

/**
 * ActionGroup 配置工厂函数
 * @param config ActionGroup 配置
 * @returns 完整的 ActionGroup 配置
 */
export const createActionGroupConfig = (
	config: ActionGroupButtonConfig,
): ActionGroupButtonConfig => ({
	...config,
});

/**
 * 规则按钮组配置
 */
export const ruleButtonGroups: ButtonGroupConfig[] = [
	createButtonGroupConfig({
		title: "页面切换",
		tooltipContent: "控制规则页面切换",
		buttons: [
			{ text: "第一页", styleType: ButtonStyleType.INFO_DISPLAY },
			{ text: "第二页", styleType: ButtonStyleType.INFO_DISPLAY },
		],
	}),
];

/**
 * 动态终极PK按钮组配置函数
 * 根据当前阶段动态禁用/启用按钮组
 */
export function createFinalPKButtonGroups(): ButtonGroupConfig[] {
	// 检查是否为自由辩论阶段（暂时未使用，保留以备后续实现按钮禁用逻辑）
	// const controls = window.ultimatePKControls;
	// const isFreeDabateStage = controls?.isFreeDabateStage?.() || false;

	return [
		createButtonGroupConfig({
			title: "阶段切换",
			tooltipContent: "控制终极PK环节切换",
			buttons: [
				{
					text: "观点陈述",
					styleType: ButtonStyleType.NAVIGATION,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.setStage(PKStage.VIEWPOINT);
						}
					}
				},
				{
					text: "双方质询",
					styleType: ButtonStyleType.NAVIGATION,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.setStage(PKStage.INQUIRY);
						}
					}
				},
				{
					text: "自由辩论",
					styleType: ButtonStyleType.NAVIGATION,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.setStage(PKStage.FREE_DEBATE);
						}
					}
				},
				{
					text: "总结陈词",
					styleType: ButtonStyleType.NAVIGATION,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.setStage(PKStage.SUMMARY);
						}
					}
				},
			],
		}),
		createButtonGroupConfig({
			title: "正方控制",
			tooltipContent: "控制正方计时",
			buttons: [
				{
					text: "计时开始",
					styleType: ButtonStyleType.CONTENT_DISPLAY,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.startPositiveTimer();
						}
					},
					get isDisabled() {
						const controls = window.ultimatePKControls;
						return controls?.shouldDisablePositiveControls?.() || false;
					}
				},
				{
					text: "计时暂停",
					styleType: ButtonStyleType.CONTENT_DISPLAY,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.pausePositiveTimer();
						}
					},
					get isDisabled() {
						const controls = window.ultimatePKControls;
						return controls?.shouldDisablePositiveControls?.() || false;
					}
				},
				{
					text: "计时重置",
					styleType: ButtonStyleType.CONTENT_DISPLAY,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.resetPositiveTimer();
						}
					},
					get isDisabled() {
						const controls = window.ultimatePKControls;
						return controls?.shouldDisablePositiveControls?.() || false;
					}
				},
			],
		}),
		createButtonGroupConfig({
			title: "反方控制",
			tooltipContent: "控制反方计时",
			buttons: [
				{
					text: "计时开始",
					styleType: ButtonStyleType.CONTENT_DISPLAY,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.startNegativeTimer();
						}
					},
					get isDisabled() {
						const controls = window.ultimatePKControls;
						return controls?.shouldDisableNegativeControls?.() || false;
					}
				},
				{
					text: "计时暂停",
					styleType: ButtonStyleType.CONTENT_DISPLAY,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.pauseNegativeTimer();
						}
					},
					get isDisabled() {
						const controls = window.ultimatePKControls;
						return controls?.shouldDisableNegativeControls?.() || false;
					}
				},
				{
					text: "计时重置",
					styleType: ButtonStyleType.CONTENT_DISPLAY,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.resetNegativeTimer();
						}
					},
					get isDisabled() {
						const controls = window.ultimatePKControls;
						return controls?.shouldDisableNegativeControls?.() || false;
					}
				},
			],
		}),
		createButtonGroupConfig({
			title: "全局控制",
			tooltipContent: "控制全局终极PK功能",
			buttons: [
				{
					text: "全部暂停",
					styleType: ButtonStyleType.PRIMARY_ACTION,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.pauseAllTimers();
						}
					}
				},
				{
					text: "切换计时",
					styleType: ButtonStyleType.PRIMARY_ACTION,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.switchTimer();
						}
					}
				},
				{
					text: "短铃提醒",
					styleType: ButtonStyleType.PRIMARY_ACTION,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.playShortBell();
						}
					}
				},
				{
					text: "长铃提醒",
					styleType: ButtonStyleType.PRIMARY_ACTION,
					onPress: () => {
						const controls = window.ultimatePKControls;
						if (controls) {
							controls.playLongBell();
						}
					}
				},
			],
		}),
	];
}

/**
 * 题目按钮组配置（静态版本，保持向后兼容）
 */
export const finalPKButtonGroups: ButtonGroupConfig[] = createFinalPKButtonGroups();

/**
	 * 题目按钮组配置
	 *
	 * @param onQuestionPrevious 上一题回调
	 * @param onQuestionJump 跳转题目回调
	 * @param onQuestionNext 下一题回调
	 * @param onStageChange 阶段切换回调
	 * @param onPackageChange 题包切换回调（会显示全局确认对话框）
	 * @param onPackageChangeConfirmed 题包切换确认后的直接执行回调（绕过全局确认对话框）
	 * @param onToggleAnswer 答案显示切换回调
	 * @param currentQuestionNumber 当前题目序号（用于同步表单字段显示）
	 * @param totalQuestions 题目总数（用于设置题号输入范围限制）
	 * @returns 题目按钮组配置数组
	 */
export function createQuestionButtonGroups(
	onQuestionPrevious?: () => void,
	onQuestionJump?: (formState: Record<string, string | number>) => void,
	onQuestionNext?: () => void,
	onStageChange?: (stageName: string) => void,
	onPackageChange?: (packageId: string, packageName: string) => void,
	onPackageChangeConfirmed?: (packageId: string, packageName: string) => void,
	onToggleAnswer?: () => void,
	currentQuestionNumber?: number,
	totalQuestions?: number
): ButtonGroupConfig[] {
	const buttonGroups: ButtonGroupConfig[] = [];

	// 只有在传入阶段切换回调时才添加阶段切换按钮组
	if (onStageChange) {
		buttonGroups.push(createActionGroupConfig({
			type: 'actionGroup',
			title: "阶段切换",
			tooltipContent: "控制题目阶段",
			selectionMode: 'single',
			disallowEmptySelection: true,
			defaultSelectedKeys: ['必答题'],
			items: [
				{ key: '必答题', label: '必答题' },
				{ key: '挑战题', label: '挑战题' },
			],
			onSelectionChange: (key: string) => {
				console.log('[createQuestionButtonGroups] 阶段切换被调用', { key, timestamp: Date.now() });
				onStageChange(key);
			},
		}));
	}

	// 只有在传入题包切换回调时才添加题包切换按钮组
	if (onPackageChange) {
		buttonGroups.push(createActionGroupConfig({
			type: 'actionGroup',
			title: "题包切换",
			tooltipContent: "控制题包选择",
			selectionMode: 'single',
			disallowEmptySelection: true,
			defaultSelectedKeys: ['正式题包'],
			items: [
				{ key: '正式题包', label: '正式题包' },
				{ key: '备用题包', label: '备用题包' },
			],
			onSelectionChange: (key: string) => {
				// 如果有确认后的直接执行回调，使用它（绕过全局确认对话框）
				const executeCallback = onPackageChangeConfirmed || onPackageChange;
				// 根据选择的key映射到对应的包ID和包名
				if (key === '正式题包') {
					executeCallback("1", "正式题包");
				} else if (key === '备用题包') {
					executeCallback("2", "备用题包");
				}
			},
			// 添加确认对话框配置
			confirmationDialog: {
				title: "题包切换确认",
				content: "即将切换到：{targetLabel}",
				confirmLabel: "确认",
				cancelLabel: "取消",
				variant: "confirmation"
			}
		}));
	}

	// 题目切换按钮组（始终显示）
	buttonGroups.push(createButtonGroupConfig({
		title: "题目切换",
		tooltipContent: "控制题目区域",
		formFields: [
			{
				label: "题号",
				name: "questionNumber",
				type: "number",
				defaultValue: currentQuestionNumber || 0,
				minValue: 0,
				maxValue: totalQuestions && totalQuestions > 0 ? totalQuestions : 1
			},
		],
		buttons: [
			{
				text: "上一题",
				styleType: ButtonStyleType.PRIMARY_ACTION,
				onPress: onQuestionPrevious
			},
			{
				text: "跳转",
				styleType: ButtonStyleType.PRIMARY_ACTION,
				dynamicHandler: onQuestionJump
			},
			{
				text: "下一题",
				styleType: ButtonStyleType.PRIMARY_ACTION,
				onPress: onQuestionNext
			},
		],
		showBreakBeforeButtons: true,
	}),
		createButtonGroupConfig({
			title: "答题流程",
			tooltipContent: "控制题目区域",
			buttons: [
				{ text: "正确答案", styleType: ButtonStyleType.CONTENT_DISPLAY, onPress: onToggleAnswer },
				{ text: "选手答案", styleType: ButtonStyleType.CONTENT_DISPLAY },
				{ text: "答案解析", styleType: ButtonStyleType.CONTENT_DISPLAY },
			],
		}),
		createButtonGroupConfig({
			title: "填空题切换",
			tooltipContent: "控制题目区域",
			buttons: [
				{ text: "填空放大", styleType: ButtonStyleType.SPECIAL_FUNCTION },
			],
			formFields: [{ label: "选手", name: "playerNumber", defaultValue: 0 }],
			additionalButtons: [
				{ text: "上一位", styleType: ButtonStyleType.PRIMARY_ACTION },
				{ text: "跳转", styleType: ButtonStyleType.PRIMARY_ACTION },
				{ text: "下一位", styleType: ButtonStyleType.PRIMARY_ACTION },
			],
			renderOrder: ["buttons", "formFields", "additionalButtons"],
			showBreakBeforeAdditionalButtons: true,
		}),
		createButtonGroupConfig({
			title: "故障手动",
			tooltipContent: "控制题目区域",
			buttons: [
				{ text: "强制提交", styleType: ButtonStyleType.DANGER_ACTION },
				{ text: "倒计时", styleType: ButtonStyleType.DANGER_ACTION },
				{ text: "同步数据", styleType: ButtonStyleType.DANGER_ACTION },
			],
		}));

	return buttonGroups;
}

/**
 * 题目按钮组配置（静态版本，保持向后兼容）
 */
export const questionButtonGroups: ButtonGroupConfig[] = createQuestionButtonGroups();

/**
 * 动态排名按钮组配置函数
 * 根据分页状态动态配置按钮的启用/禁用状态和事件处理器
 */
export function createRankingButtonGroups(
	currentPage: number = 1,
	totalPages: number = 1,
	hasMultiplePages: boolean = false,
	onPageChange?: (page: number) => void
): ButtonGroupConfig[] {
	return [
		createButtonGroupConfig({
			title: "数据切换",
			tooltipContent: "控制排行榜显示",
			buttons: [
				{ text: "成绩赋分", styleType: ButtonStyleType.PRIMARY_ACTION },
				{
					text: "第一页",
					styleType: ButtonStyleType.INFO_DISPLAY,
					onPress: () => onPageChange?.(1),
					isDisabled: !hasMultiplePages || currentPage === 1
				},
				{
					text: "第二页",
					styleType: ButtonStyleType.INFO_DISPLAY,
					onPress: () => onPageChange?.(2),
					isDisabled: !hasMultiplePages || currentPage === 2 || totalPages < 2
				},
			],
		}),
	];
}

/**
 * 排名按钮组配置（静态版本，保持向后兼容）
 */
export const rankingButtonGroups: ButtonGroupConfig[] = createRankingButtonGroups();

/**
 * 争分夺秒和同分加赛按钮组配置（静态版本，保持向后兼容）
 */
export const timeRaceAndTieBreakButtonGroups: ButtonGroupConfig[] = [
	createButtonGroupConfig({
		title: "答题流程",
		tooltipContent: "控制答题流程",
		buttons: [
			{ text: "开始答题", styleType: ButtonStyleType.CONTENT_DISPLAY },
			{ text: "显示题目", styleType: ButtonStyleType.CONTENT_DISPLAY },
			{ text: "显示进度", styleType: ButtonStyleType.CONTENT_DISPLAY },
		],
	}),
	createButtonGroupConfig({
		title: "题目切换",
		tooltipContent: "控制题目切换",
		formFields: [
			{ label: "题号", name: "questionNumber", defaultValue: 0 },
		],
		buttons: [
			{ text: "上一题", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "跳转", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "下一题", styleType: ButtonStyleType.PRIMARY_ACTION },
		],
		showBreakBeforeButtons: true,
	}),
	createButtonGroupConfig({
		title: "排行榜",
		tooltipContent: "控制排行榜显示",
		buttons: [
			{ text: "成绩赋分", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "第一页", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "第二页", styleType: ButtonStyleType.PRIMARY_ACTION },
		],
	}),
];

/**
 * 动态争分夺秒和同分加赛按钮组配置函数
 * 根据内容类型动态生成按钮组，支持比赛计时功能
 *
 * @param contentType 内容类型，如"快答180"、"快答120"、"快答"等
 * @param nodeName 节点名称（可选），用于从名称中提取时长
 * @param onLog 日志记录回调函数
 * @param onPackageChange 题包切换回调函数（可选）
 * @param onQuestionPrevious 上一题回调函数（可选）
 * @param onQuestionJump 跳转题目回调函数（可选）
 * @param onQuestionNext 下一题回调函数（可选）
 * @param currentQuestionNumber 当前题目序号（可选）
 * @param totalQuestions 题目总数（可选）
 * @returns 按钮组配置数组
 */
export function createTimeRaceAndTieBreakButtonGroups(
	contentType?: string,
	nodeName?: string,
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void,
	onPackageChange?: (packageId: string, packageName: string) => void,
	onPackageChangeConfirmed?: (packageId: string, packageName: string) => void,
	onQuestionPrevious?: () => void,
	onQuestionJump?: (formState: Record<string, string | number>) => void,
	onQuestionNext?: () => void,
	currentQuestionNumber?: number,
	totalQuestions?: number,
	onScoreAssignment?: () => void,
	onPageFirst?: () => void,
	onPageSecond?: () => void,
	paginationInfo?: {
		hasMultiplePages: boolean;
		currentPage: number;
		totalPages: number;
	}
): ButtonGroupConfig[] {
	console.log('[按钮组配置] createTimeRaceAndTieBreakButtonGroups 函数被调用', {
		contentType,
		nodeName,
		hasOnPageFirst: !!onPageFirst,
		hasOnPageSecond: !!onPageSecond,
		hasPaginationInfo: !!paginationInfo,
		timestamp: Date.now()
	});

	// 检查是否为争分夺秒类型
	const isTimeRace = contentType && isTimeRaceContentType(contentType, nodeName);
	const timeInSeconds = isTimeRace ? parseTimeRaceContentType(contentType, nodeName) : null;

	// 检查是否为争分夺秒或同分加赛环节（用于分页按钮显示）
	const isTimeRaceSection = contentType === "快答" ||
		(nodeName && (nodeName.includes('争分夺秒') || nodeName.includes('同分加赛')));

	console.log('[按钮组配置] 计时和分页条件检查', {
		isTimeRace,
		timeInSeconds,
		isTimeRaceSection,
		willShowTimer: isTimeRace && timeInSeconds,
		timestamp: Date.now()
	});

	// 如果是争分夺秒类型，显示计时按钮组
	if (isTimeRace && timeInSeconds) {
		// 检查当前计时器状态
		const currentState = globalTimerManager.getState();

		// 如果计时器未初始化或时间不匹配，重新初始化
		if (currentState.maxTime !== timeInSeconds || currentState.time === 0) {
			globalTimerManager.cleanup();
			globalTimerManager.initialize(timeInSeconds, onLog);
		}

		// 格式化初始时间显示
		const formatTime = (time: number): string => {
			if (isNaN(time)) return "00:00";
			const minutes = Math.floor(time / 60);
			const seconds = Math.floor(time % 60);
			return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
		};

		// 添加UI更新监听器
		const updateTimerDisplay = () => {
			const timerElements = document.querySelectorAll('.timer-display');
			const currentTime = globalTimerManager.getFormattedTime();
			const state = globalTimerManager.getState();

			timerElements.forEach(element => {
				if (element.textContent !== currentTime) {
					element.textContent = currentTime;

					// 添加视觉反馈
					element.classList.remove('warning', 'danger');
					if (state.time <= 30 && state.time > 10) {
						element.classList.add('warning');
					} else if (state.time <= 10) {
						element.classList.add('danger');
					}
				}
			});

			// 更新开始/暂停按钮文本
			const startButtons = document.querySelectorAll('button');
			startButtons.forEach(button => {
				if (button.textContent === '比赛开始' || button.textContent === '暂停') {
					button.textContent = state.isRunning ? '暂停' : '比赛开始';
				}
			});
		};

		// 添加UI更新监听器
		globalTimerManager.addListener(updateTimerDisplay);

		return [
			// 题包切换按钮组（只有在传入回调时才显示）
			...(onPackageChange ? [createActionGroupConfig({
				type: 'actionGroup',
				title: "题包切换",
				tooltipContent: "控制题包选择",
				selectionMode: 'single',
				disallowEmptySelection: true,
				defaultSelectedKeys: ['正式题包'],
				items: [
					{ key: '正式题包', label: '正式题包' },
					{ key: '备用题包', label: '备用题包' },
				],
				onSelectionChange: (key: string) => {
					// 如果有确认后的直接执行回调，使用它（绕过全局确认对话框）
					const executeCallback = onPackageChangeConfirmed || onPackageChange;

					// 调试日志：确认使用的是哪个回调函数
					console.log(`[题包切换调试] 选择变更: ${key}`, {
						hasOnPackageChangeConfirmed: !!onPackageChangeConfirmed,
						hasOnPackageChange: !!onPackageChange,
						executeCallbackName: executeCallback === onPackageChangeConfirmed ? 'onPackageChangeConfirmed' :
							executeCallback === onPackageChange ? 'onPackageChange' : 'unknown',
						timestamp: Date.now()
					});

					// 根据选择的key映射到对应的包ID和包名
					if (key === '正式题包') {
						executeCallback("1", "正式题包");
					} else if (key === '备用题包') {
						executeCallback("2", "备用题包");
					}
				}
				// 临时移除确认对话框配置，直接执行题包切换
				// confirmationDialog: {
				// 	title: "题包切换确认",
				// 	content: "即将切换到：{targetLabel}",
				// 	confirmLabel: "确认",
				// 	cancelLabel: "取消",
				// 	variant: "confirmation"
				// }
			})] : []),
			createButtonGroupConfig({
				title: "比赛计时",
				tooltipContent: `${getTimeRaceDisplayName(contentType || '')}计时控制`,
				buttons: [
					{
						text: formatTime(timeInSeconds),
						styleType: ButtonStyleType.INFO_DISPLAY,
						isDisabled: true,
						className: "timer-display"
					}
				],
				additionalButtons: [
					{
						text: "比赛开始",
						styleType: ButtonStyleType.SPECIAL_FUNCTION,
						onPress: () => {
							try {
								const state = globalTimerManager.getState();
								if (state.isRunning) {
									globalTimerManager.pause();
								} else {
									globalTimerManager.start();
								}
								onLog?.('info', '计时器按钮被点击', {
									action: state.isRunning ? 'pause' : 'start',
									currentTime: state.time,
									timestamp: Date.now()
								});
							} catch (error) {
								onLog?.('error', '计时器操作失败', {
									error: error instanceof Error ? error.message : String(error),
									timestamp: Date.now()
								});
							}
						}
					},
					{
						text: "重置",
						styleType: ButtonStyleType.DANGER_ACTION,
						onPress: () => {
							try {
								globalTimerManager.reset();
								onLog?.('info', '计时器重置', {
									timestamp: Date.now()
								});
							} catch (error) {
								onLog?.('error', '计时器重置失败', {
									error: error instanceof Error ? error.message : String(error),
									timestamp: Date.now()
								});
							}
						}
					}
				],
				renderOrder: ["buttons", "additionalButtons"],
				showBreakBeforeAdditionalButtons: true
			}),
			// 题目切换按钮组（只有在传入回调时才显示）
			...(onQuestionPrevious || onQuestionJump || onQuestionNext ? [createButtonGroupConfig({
				title: "题目切换",
				tooltipContent: "控制题目区域",
				formFields: [
					{
						label: "题号",
						name: "questionNumber",
						type: "number",
						defaultValue: currentQuestionNumber || 0,
						minValue: 0,
						maxValue: totalQuestions && totalQuestions > 0 ? totalQuestions : 100
					},
				],
				buttons: [
					{
						text: "上一题",
						styleType: ButtonStyleType.PRIMARY_ACTION,
						onPress: onQuestionPrevious
					},
					{
						text: "跳转",
						styleType: ButtonStyleType.PRIMARY_ACTION,
						dynamicHandler: onQuestionJump
					},
					{
						text: "下一题",
						styleType: ButtonStyleType.PRIMARY_ACTION,
						onPress: onQuestionNext
					},
				],
				showBreakBeforeButtons: true,
			})] : []),
			// 数据切换按钮组（只有在传入回调时才显示）
			...(onScoreAssignment || onPageFirst || onPageSecond || paginationInfo ? [createButtonGroupConfig({
				title: "数据切换",
				tooltipContent: "控制排名数据显示和分页",
				buttons: [
					{
						text: "成绩赋分",
						styleType: ButtonStyleType.DATA_OPERATION,
						onPress: onScoreAssignment
					},
					{
						text: "第一页",
						styleType: ButtonStyleType.NAVIGATION,
						onPress: onPageFirst,
						isDisabled: (() => {
							// 如果有分页信息，使用智能逻辑；否则使用简单逻辑
							if (paginationInfo) {
								const hasMultiplePages = paginationInfo.hasMultiplePages || false;
								const currentPage = paginationInfo.currentPage || 1;
								// 第一页按钮：当前在第1页或没有多页时禁用
								return !hasMultiplePages || currentPage === 1;
							}
							return !onPageFirst;
						})()
					},
					{
						text: "第二页",
						styleType: ButtonStyleType.NAVIGATION,
						onPress: onPageSecond,
						isDisabled: (() => {
							// 如果有分页信息，使用智能逻辑；否则使用简单逻辑
							if (paginationInfo) {
								const hasMultiplePages = paginationInfo.hasMultiplePages || false;
								const currentPage = paginationInfo.currentPage || 1;
								const totalPages = paginationInfo.totalPages || 1;
								// 第二页按钮：没有多页、当前在第2页或总页数<2时禁用
								const shouldDisable = !hasMultiplePages || currentPage === 2 || totalPages < 2;
								console.log('[按钮组配置] 第二页按钮禁用状态计算', {
									hasMultiplePages,
									currentPage,
									totalPages,
									shouldDisable,
									paginationInfo,
									onPageSecond: !!onPageSecond,
									timestamp: Date.now()
								});
								return shouldDisable;
							}
							const fallbackDisabled = !onPageSecond;
							console.log('[按钮组配置] 第二页按钮使用简单逻辑', {
								onPageSecond: !!onPageSecond,
								fallbackDisabled,
								timestamp: Date.now()
							});
							return fallbackDisabled;
						})()
					},
				],
			})] : [])
		];
	}

	// 返回计时器按钮组（已包含分页逻辑）
	return timeRaceAndTieBreakButtonGroups;
}

/**
 * 首页按钮组配置
 */
export const homeButtonGroups: ButtonGroupConfig[] = [
	createButtonGroupConfig({
		title: "选手显示",
		tooltipContent: "控制选手显示功能",
		formFields: [
			{
				label: "选择选手",
				name: "playerSelection",
				type: "picker",
				placeholder: "请选择选手",
				options: [
					{ key: "player1", label: "选手一" },
					{ key: "player2", label: "选手二" },
					{ key: "player3", label: "选手三" },
				],
			},
		],
		buttons: [
			{ text: "显示选手", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "切回首页", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
		],
	}),
	createButtonGroupConfig({
		title: "领导显示",
		tooltipContent: "控制领导显示功能",
		formFields: [
			{
				label: "选择领导",
				name: "leaderSelection",
				type: "picker",
				placeholder: "请选择领导",
				selectedKey: undefined, // 初始不选择任何选项
				options: [
					{ key: "leader1", label: "领导一" },
					{ key: "leader2", label: "领导二" },
					{ key: "leader3", label: "领导三" },
				],
				onSelectionChange: (key: string) => {
					console.log(`领导选择已更改为: ${key}`);
				},
			},
		],
		buttons: [
			{ text: "显示领导", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "切回首页", styleType: ButtonStyleType.PRIMARY_ACTION },
			{
				text: "下一个",
				styleType: ButtonStyleType.PRIMARY_ACTION,
				// "下一个"按钮会自动绑定切换逻辑，无需手动配置 dynamicHandler
			},
		],
	}),
	createButtonGroupConfig({
		title: "奖项显示",
		tooltipContent: "控制奖项显示功能",
		formFields: [
			{
				label: "选择奖项",
				name: "awardSelection",
				type: "picker",
				placeholder: "请选择奖项",
				options: [
					{ key: "champion", label: "个人挑战赛冠军" },
					{ key: "organization", label: "优秀组织奖" },
					{ key: "winner", label: "优胜奖" },
				],
			},
		],
		buttons: [
			{ text: "显示奖项", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "切回首页", styleType: ButtonStyleType.PRIMARY_ACTION },
			{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
		],
	}),
];

/**
 * 空按钮组配置（用于未知节点）
 */
export const emptyButtonGroups: ButtonGroupConfig[] = [];

// ==================== 动态配置生成函数 ====================

/**
 * 将配置数据转换为选择器选项
 */
function convertToPickerOptions(items: ProcessedConfigurationItem[]): Array<{ key: string; label: string }> {
	return items.map(item => ({
		key: item.id.toString(),
		label: item.title,
	}));
}

/**
 * 生成动态首页按钮组配置
 *
 * @param configurationData 分组的配置数据
 * @param onLeaderSelectionChange 领导选择变更回调
 * @param onPlayerSelectionChange 选手选择变更回调
 * @param onAwardSelectionChange 奖项选择变更回调
 * @param onResetToMainScreen 回到主屏回调
 * @param onShowContentWithValidation 带验证的显示内容回调
 * @returns 动态生成的按钮组配置数组
 */
export function generateHomeButtonGroups(
	configurationData: GroupedConfigurationData,
	onLeaderSelectionChange?: (key: string) => void,
	onPlayerSelectionChange?: (key: string) => void,
	onAwardSelectionChange?: (key: string) => void,
	onResetToMainScreen?: () => void,
	onShowContentWithValidation?: (type: 'leader' | 'player' | 'award', formState: Record<string, string | number>) => void
): ButtonGroupConfig[] {
	const buttonGroups: ButtonGroupConfig[] = [];

	// 生成选手显示按钮组
	if (configurationData.playerDisplay.length > 0) {
		buttonGroups.push(createButtonGroupConfig({
			title: "选手显示",
			tooltipContent: "控制选手显示功能",
			formFields: [
				{
					label: "选择选手",
					name: "playerSelection",
					type: "picker",
					placeholder: "请选择选手",
					selectedKey: undefined,
					options: convertToPickerOptions(configurationData.playerDisplay),
					onSelectionChange: onPlayerSelectionChange || ((key: string) => {
						console.log("选手选择变更:", key);
					}),
				},
			],
			buttons: [
				{ text: "显示选手", styleType: ButtonStyleType.PRIMARY_ACTION, dynamicHandler: (formState) => onShowContentWithValidation?.('player', formState) },
				{ text: "回到主屏", styleType: ButtonStyleType.PRIMARY_ACTION, onPress: onResetToMainScreen },
				{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
			],
		}));
	}

	// 生成领导显示按钮组
	if (configurationData.leaderDisplay.length > 0) {
		buttonGroups.push(createButtonGroupConfig({
			title: "领导显示",
			tooltipContent: "控制领导显示功能",
			formFields: [
				{
					label: "选择领导",
					name: "leaderSelection",
					type: "picker",
					placeholder: "请选择领导",
					selectedKey: undefined,
					options: convertToPickerOptions(configurationData.leaderDisplay),
					onSelectionChange: onLeaderSelectionChange || ((key: string) => {
						console.log("领导选择变更:", key);
					}),
				},
			],
			buttons: [
				{ text: "显示领导", styleType: ButtonStyleType.PRIMARY_ACTION, dynamicHandler: (formState) => onShowContentWithValidation?.('leader', formState) },
				{ text: "回到主屏", styleType: ButtonStyleType.PRIMARY_ACTION, onPress: onResetToMainScreen },
				{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
			],
		}));
	}

	// 生成奖项显示按钮组
	if (configurationData.awardDisplay.length > 0) {
		buttonGroups.push(createButtonGroupConfig({
			title: "奖项显示",
			tooltipContent: "控制奖项显示功能",
			formFields: [
				{
					label: "选择奖项",
					name: "awardSelection",
					type: "picker",
					placeholder: "请选择奖项",
					selectedKey: undefined,
					options: convertToPickerOptions(configurationData.awardDisplay),
					onSelectionChange: onAwardSelectionChange || ((key: string) => {
						console.log("奖项选择变更:", key);
					}),
				},
			],
			buttons: [
				{ text: "显示奖项", styleType: ButtonStyleType.PRIMARY_ACTION, dynamicHandler: (formState) => onShowContentWithValidation?.('award', formState) },
				{ text: "回到主屏", styleType: ButtonStyleType.PRIMARY_ACTION, onPress: onResetToMainScreen },
				{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
			],
		}));
	}

	return buttonGroups;
}

// ==================== 安全公开课按钮组配置 ====================

/**
 * 创建安全公开课按钮组配置
 * 包含三个按钮组：绑定Vmix、节目显示、选手打分
 *
 * @param programDisplayOptions 节目显示选项数据
 * @param onVmixLinkChange Vmix链接变更回调
 * @param onProgramDisplayChange 节目显示变更回调
 * @param onPlayerScoringChange 选手打分变更回调
 * @returns 安全公开课按钮组配置数组
 */
export function createSafetyPublicClassButtonGroups(
	programDisplayOptions: Array<{ key: string; label: string }> = [],
	onVmixLinkChange?: (link: string) => void,
	onProgramDisplayChange?: (key: string) => void,
	onPlayerScoringChange?: (key: string) => void,
	mutualExclusiveControl?: {
		triggerReset: (selectedType: 'program' | 'player') => void;
	}
): ButtonGroupConfig[] {
	return [
		// 按钮组 1：绑定Vmix
		createButtonGroupConfig({
			title: "绑定Vmix",
			tooltipContent: "配置Vmix链接地址",
			formFields: [
				{
					label: "链接地址",
					name: "vmixLink",
					type: "text",
					textValue: "",
					onTextChange: onVmixLinkChange,
				},
			],
			buttons: [], // 绑定Vmix按钮组只包含输入字段，无操作按钮
		}),

		// 按钮组 2：节目显示
		createButtonGroupConfig({
			title: "节目显示",
			tooltipContent: "控制节目显示功能",
			formFields: [
				{
					label: "选择节目",
					name: "programSelection",
					type: "picker",
					placeholder: "请选择节目",
					options: programDisplayOptions,
					onSelectionChange: (key: string) => {
						// 先触发重置（重置选手选择）
						mutualExclusiveControl?.triggerReset('program');
						// 然后执行节目显示回调
						onProgramDisplayChange?.(key);
					},
				},
			],
			buttons: [
				{ text: "显示标题", styleType: ButtonStyleType.PRIMARY_ACTION, dynamicHandler: (_formState) => { /* 验证逻辑在SidebarButtonGroup中处理 */ } },
				{ text: "切换PPT", styleType: ButtonStyleType.PRIMARY_ACTION },
				{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
			],
		}),

		// 按钮组 3：选手打分
		createButtonGroupConfig({
			title: "选手打分",
			tooltipContent: "控制选手打分功能",
			formFields: [
				{
					label: "选择选手",
					name: "playerSelection",
					type: "picker",
					placeholder: "请选择选手",
					options: programDisplayOptions, // 使用相同的数据源
					onSelectionChange: (key: string) => {
						// 先触发重置（重置节目选择）
						mutualExclusiveControl?.triggerReset('player');
						// 然后执行选手打分回调
						onPlayerScoringChange?.(key);
					},
				},
			],
			buttons: [
				{ text: "开启打分", styleType: ButtonStyleType.PRIMARY_ACTION, dynamicHandler: (_formState) => { /* 验证逻辑在SidebarButtonGroup中处理 */ } },
				{ text: "回到主屏", styleType: ButtonStyleType.PRIMARY_ACTION },
				{ text: "下一个", styleType: ButtonStyleType.PRIMARY_ACTION },
			],
		}),
	];
}

/**
 * 获取节目显示数据并转换为选择器选项
 * 从配置信息表中筛选"节目显示"类型的数据
 *
 * @param tableId 配置信息表ID
 * @returns 节目显示选项数组
 */
export async function getProgramDisplayOptions(tableId: string): Promise<Array<{ key: string; label: string }>> {
	try {
		// 使用 NavigationApiService 获取配置数据，筛选"节目显示"类型
		const response = await NavigationApiService.getConfigurationData(tableId);

		// 筛选出"节目显示"类型的数据
		const programDisplayItems = response.data.list.filter(item =>
			item["信息类型"] === "节目显示"
		);

		// 转换为选择器选项格式
		return programDisplayItems.map(item => ({
			key: item.Id.toString(),
			label: item["标题"] || `节目${item.Id}`,
		}));
	} catch (error) {
		console.error('[安全公开课] 获取节目显示数据失败:', error);
		// 返回空数组作为降级处理
		return [];
	}
}

/**
 * 获取选手打分数据并转换为选择器选项
 * 从配置信息表中筛选"选手打分"类型的数据
 *
 * @param tableId 配置信息表ID
 * @returns 选手打分选项数组
 */
export async function getPlayerScoringOptions(tableId: string): Promise<Array<{ key: string; label: string }>> {
	try {
		// 使用 NavigationApiService 获取配置数据，筛选"选手打分"类型
		const response = await NavigationApiService.getConfigurationData(tableId);

		// 筛选出"选手打分"类型的数据
		const playerScoringItems = response.data.list.filter(item =>
			item["信息类型"] === "选手打分" || item["信息类型"] === "选手"
		);

		// 转换为选择器选项格式
		return playerScoringItems.map(item => ({
			key: item.Id.toString(),
			label: (item["标题"] || item["姓名"] || `选手${item.Id}`) as string,
		}));
	} catch (error) {
		console.error('[安全公开课] 获取选手打分数据失败:', error);
		// 返回空数组作为降级处理
		return [];
	}
}

/**
 * 创建带数据加载的安全公开课按钮组配置
 * 支持动态加载节目显示和选手打分数据
 *
 * @param nodeName 节点名称（用于筛选特定环节的数据）
 * @param onLog 日志记录回调函数
 * @returns 安全公开课按钮组配置数组
 */
export function createSafetyPublicClassButtonGroupsWithData(
	nodeName?: string,
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void
): ButtonGroupConfig[] {
	onLog?.('info', `[安全公开课] 初始化${nodeName || '安全公开课'}环节按钮组`);

	// 创建基础按钮组配置，数据将异步加载
	return createSafetyPublicClassButtonGroups(
		[], // 初始为空，数据将异步加载
		(link: string) => {
			onLog?.('info', `[安全公开课] Vmix链接已更新: ${link}`);
		},
		(key: string) => {
			onLog?.('info', `[安全公开课] 节目显示已切换: ${key}`);
		},
		(key: string) => {
			onLog?.('info', `[安全公开课] 选手打分已切换: ${key}`);
		}
	);
}

/**
 * 创建带实际数据的安全公开课按钮组配置
 * 支持从NavigationApi配置数据中提取节目显示和选手打分数据
 *
 * @param nodeName 节点名称（用于筛选特定环节的数据）
 * @param onLog 日志记录回调函数
 * @param configurationData 配置数据（从raceApi获取，包含节目显示和选手显示数据）
 * @param onStateChange 状态更新回调函数（可选）
 * @param onResetControlChange 重置控制状态更新回调函数（可选）
 * @returns 安全公开课按钮组配置数组
 */
export function createSafetyPublicClassButtonGroupsWithRealData(
	_nodeName?: string,
	onLog?: (level: 'info' | 'warning' | 'error' | 'success', message: string, details?: unknown) => void,
	configurationData?: unknown,
	onStateChange?: (state: { displayType: 'program' | 'player' | null; selectedItemId: string | null }) => void,
	onResetControlChange?: (resetControl: { timestamp: number; selectedType: 'program' | 'player' | null }) => void
): ButtonGroupConfig[] {
	// 初始化选项数组
	let programDisplayOptions: Array<{ key: string; label: string }> = [];
	let playerScoringOptions: Array<{ key: string; label: string }> = [];

	// 从配置数据中提取节目显示和选手打分选项
	if (configurationData) {
		try {
			const config = configurationData as Record<string, unknown>;

			// 提取节目显示选项 - 从专门的 programDisplay 分组中获取
			programDisplayOptions = (config.programDisplay && Array.isArray(config.programDisplay) ? config.programDisplay : [])
				.map((item: Record<string, unknown>) => ({
					key: (item.id || item.Id || item["信息 ID"])?.toString() || Math.random().toString(),
					label: (item.title || item["标题"])?.toString() || `节目${item.id || item.Id || item["信息 ID"] || '未知'}`
				}));

			// 提取选手打分选项 - 从专门的 playerDisplay 分组中获取
			playerScoringOptions = (config.playerDisplay && Array.isArray(config.playerDisplay) ? config.playerDisplay : [])
				.map((item: Record<string, unknown>) => ({
					key: (item.id || item.Id || item["信息 ID"])?.toString() || Math.random().toString(),
					label: (item.title || item["标题"])?.toString() || `选手${item.id || item.Id || item["信息 ID"] || '未知'}`
				}));



			onLog?.('info', `[安全公开课] 从配置数据提取节目显示选项: ${programDisplayOptions.length}个`);
			onLog?.('info', `[安全公开课] 从配置数据提取选手打分选项: ${playerScoringOptions.length}个`);
		} catch (error) {
			console.error('[安全公开课] 处理配置数据时出错:', error);
			onLog?.('error', `[安全公开课] 处理配置数据失败: ${error instanceof Error ? error.message : '未知错误'}`);
		}
	}

	// 如果没有数据，提供默认的空状态提示
	if (programDisplayOptions.length === 0) {
		programDisplayOptions = [{ key: 'no-data', label: '暂无节目数据' }];
	}

	if (playerScoringOptions.length === 0) {
		playerScoringOptions = [{ key: 'no-data', label: '暂无选手数据' }];
	}

	// 创建互斥控制机制
	const mutualExclusiveControl = {
		triggerReset: (selectedType: 'program' | 'player') => {
			// 触发重置控制状态更新
			onResetControlChange?.({
				timestamp: Date.now(),
				selectedType: selectedType
			});
		}
	};

	// 创建按钮组配置
	const buttonGroups = createSafetyPublicClassButtonGroups(
		programDisplayOptions,
		(link: string) => {
			onLog?.('info', `[安全公开课] Vmix链接已更新: ${link}`);
		},
		(key: string) => {
			const selectedProgram = programDisplayOptions.find(option => option.key === key);
			if (key !== 'no-data') {
				onLog?.('info', `[安全公开课] 节目显示: ${selectedProgram?.label || key}`);
			}
			// 更新状态：节目显示
			onStateChange?.({
				displayType: 'program',
				selectedItemId: key
			});
		},
		(key: string) => {
			const selectedPlayer = playerScoringOptions.find(option => option.key === key);
			if (key !== 'no-data') {
				onLog?.('info', `[安全公开课] 选手打分: ${selectedPlayer?.label || key}`);
			}
			// 更新状态：选手显示
			onStateChange?.({
				displayType: 'player',
				selectedItemId: key
			});
		},
		mutualExclusiveControl
	);

	// 为选手打分组件使用专门的选手数据
	return buttonGroups.map(group => {
		if (group.title === "选手打分" && 'formFields' in group && group.formFields) {
			const formGroup = group as TraditionalButtonGroupConfig;
			return {
				...group,
				formFields: formGroup.formFields?.map((field: FormFieldConfig) => {
					if (field.name === "playerSelection") {
						return {
							...field,
							options: playerScoringOptions,
							placeholder: playerScoringOptions.length > 0 && playerScoringOptions[0].key !== 'no-data'
								? "请选择选手"
								: "暂无选手数据"
						};
					}
					return field;
				})
			};
		}
		return group;
	});
}

/**
 * 安全公开课按钮组配置（静态版本）
 * 使用空的选项数组，实际数据将通过动态函数获取
 */
export const safetyPublicClassButtonGroups: ButtonGroupConfig[] = createSafetyPublicClassButtonGroups();

