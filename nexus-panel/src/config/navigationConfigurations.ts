// 导航配置模块
import type { NavigationNode } from "../services/api";
import {
	type ComponentConfig,
	CONTENT_TYPE_MAPPING,
	getCurrentContentType
} from "./componentConfigurations";

/**
 * 静态导航节点映射接口
 */
export interface StaticNavigationNode {
	id: string;
	label: string;
	parentId?: string;
}

/**
 * 导航节点映射数据
 */
export const navigationNodes: Record<string, StaticNavigationNode> = {
	home: { id: "home", label: "首页" },
	"rules-intro": { id: "rules-intro", label: "规则介绍" },
	"rules-qa": { id: "rules-qa", label: "有问必答", parentId: "rules-intro" },
	"rules-onestation": {
		id: "rules-onestation",
		label: "一站到底",
		parentId: "rules-intro",
	},
	"rules-timerace": {
		id: "rules-timerace",
		label: "争分夺秒",
		parentId: "rules-intro",
	},
	"rules-finalpk": {
		id: "rules-finalpk",
		label: "终极PK",
		parentId: "rules-intro",
	},
	"rules-tiebreak": {
		id: "rules-tiebreak",
		label: "同分加赛",
		parentId: "rules-intro",
	},
	"rules-scoring": {
		id: "rules-scoring",
		label: "积分办法",
		parentId: "rules-intro",
	},
	"section-switch": { id: "section-switch", label: "环节切换" },
	"switch-qa": {
		id: "switch-qa",
		label: "有问必答",
		parentId: "section-switch",
	},
	"switch-onestation": {
		id: "switch-onestation",
		label: "一站到底",
		parentId: "section-switch",
	},
	"switch-timerace": {
		id: "switch-timerace",
		label: "争分夺秒",
		parentId: "section-switch",
	},
	"switch-finalpk": {
		id: "switch-finalpk",
		label: "终极PK",
		parentId: "section-switch",
	},
	"switch-tiebreak": {
		id: "switch-tiebreak",
		label: "同分加赛",
		parentId: "section-switch",
	},
	"switch-ranking": {
		id: "switch-ranking",
		label: "总分排名",
		parentId: "section-switch",
	},
};

/**
 * 获取静态组件配置（降级逻辑）
 * @param selectedKey 当前选中的导航键
 * @returns 组件配置或null
 */
export const getStaticComponentConfig = (selectedKey: string | null): ComponentConfig | null => {
	if (!selectedKey) return null;

	// 规则介绍子节点判断
	const currentNode = navigationNodes[selectedKey];
	if (currentNode?.parentId === "rules-intro") {
		return {
			skeleton: "RuleIntroSkeleton",
			buttonGroups: "ruleButtonGroups",
			showBreadcrumbs: true,
			showAudioPlayer: true
		};
	}

	// 其他静态节点判断
	switch (selectedKey) {
		case "switch-finalpk":
			return {
				skeleton: "UltimatePKSkeleton",
				buttonGroups: "finalPKButtonGroups",
				showBreadcrumbs: true,
				showAudioPlayer: false
			};
		case "switch-ranking":
			return {
				skeleton: "RankingSkeleton",
				buttonGroups: "rankingButtonGroups",
				showBreadcrumbs: true,
				showAudioPlayer: false
			};
		case "switch-timerace":
		case "switch-tiebreak":
			return {
				skeleton: "QuestionSkeleton",
				buttonGroups: "timeRaceAndTieBreakButtonGroups",
				showBreadcrumbs: true,
				showAudioPlayer: false
			};
		case "switch-qa":
		case "switch-onestation":
			return {
				skeleton: "QuestionSkeleton",
				buttonGroups: "questionButtonGroups",
				showBreadcrumbs: true,
				showAudioPlayer: true
			};
		default:
			return null; // 未知节点不显示按钮组
	}
};

/**
 * 获取组件配置（动态绑定 + 静态降级）
 * @param selectedKey 当前选中的导航键
 * @param navigationData 动态导航数据
 * @returns 组件配置
 */
export const getComponentConfig = (selectedKey: string | null, navigationData: NavigationNode[] | null): ComponentConfig => {
	try {
		// 如果没有选中任何节点，返回空状态配置（避免误导性日志）
		if (!selectedKey) {
			return {
				skeleton: "QuestionSkeleton",
				buttonGroups: "emptyButtonGroups",
				showBreadcrumbs: false,
				showAudioPlayer: false
			};
		}

		// 首页特殊处理
		if (selectedKey === "home") {
			return {
				skeleton: "HomePageSkeleton",
				buttonGroups: "homeButtonGroups",
				showBreadcrumbs: true,
				showAudioPlayer: false
			};
		}

		// 尝试动态绑定
		const contentType = getCurrentContentType(selectedKey, navigationData);
		if (contentType && CONTENT_TYPE_MAPPING[contentType]) {
			console.log(`[动态绑定] 成功匹配内容类型: ${contentType} -> ${CONTENT_TYPE_MAPPING[contentType].skeleton}`);
			return CONTENT_TYPE_MAPPING[contentType];
		}

		// 降级到静态判断
		console.log(`[动态绑定] 内容类型未匹配，降级到静态判断: ${selectedKey}`);
		const staticConfig = getStaticComponentConfig(selectedKey);
		if (staticConfig) {
			return staticConfig;
		}

		// 如果静态判断也返回null，说明是未知节点，返回默认配置但不显示按钮组
		console.log(`[动态绑定] 未知节点，使用默认骨架屏但不显示按钮组: ${selectedKey}`);
		return {
			skeleton: "QuestionSkeleton",
			buttonGroups: "emptyButtonGroups",
			showBreadcrumbs: false,
			showAudioPlayer: false
		};
	} catch (error) {
		console.error('[动态绑定] 获取组件配置时发生错误:', error);
		// 返回默认配置
		return {
			skeleton: "QuestionSkeleton",
			buttonGroups: "questionButtonGroups",
			showBreadcrumbs: true,
			showAudioPlayer: true
		};
	}
};
