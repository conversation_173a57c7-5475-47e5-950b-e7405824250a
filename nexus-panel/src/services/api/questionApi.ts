/**
 * 题目数据相关 API 服务
 * 
 * 提供题目数据的获取、处理和转换功能
 * 包括选项解析、音频URL提取等专用逻辑
 */

import { httpClient } from './client';
import type { ApiResponse, QuestionApiResponse, QuestionApiItem, ProcessedQuestionItem, ParsedOption, AttachmentItem } from './types';
import { ApiErrorHandler, createDataFormatError } from './errors';
import { FieldMapper, MappingDirection } from './adapters/FieldMapper';
import { TypeConverter } from './adapters/TypeConverter';

// ==================== API 配置 ====================

/**
 * 题目 API 配置
 */
const QUESTION_API_CONFIG = {
	/** 认证 Token */
	token: 'bF-rl7NY_i_A-6aO1_PN0rbsm6cHkPf3bSK0ildp',
} as const;

// ==================== 数据验证函数 ====================

/**
 * 验证题目 API 响应数据格式
 */
function validateQuestionApiResponse(response: unknown): response is QuestionApiResponse {
	if (!response || typeof response !== 'object') {
		return false;
	}

	const data = response as Record<string, unknown>;
	return (
		Array.isArray(data.list) &&
		typeof data.pageInfo === 'object' &&
		data.pageInfo !== null
	);
}

/**
 * 验证题目数据项格式（使用schema定义的英文字段名）
 */
function validateQuestionApiItem(item: unknown): item is QuestionApiItem {
	if (!item || typeof item !== 'object') {
		return false;
	}

	const question = item as Record<string, unknown>;

	// 记录实际的数据结构用于调试
	console.log('[题目数据验证] 检查数据项:', {
		Id: typeof question.Id,
		question_id: typeof question.question_id,
		question_number: typeof question.question_number,
		question_type: typeof question.question_type,
		points: typeof question.points,
		prompt: typeof question.prompt,
		options: typeof question.options,
		correct_answer: typeof question.correct_answer,
		session_id: typeof question.session_id,
		question_pack_id: typeof question.question_pack_id,
		explanation: typeof question.explanation,
		attachment_url: typeof question.attachment_url,
		stage: typeof question.stage,
		allKeys: Object.keys(question)
	});

	// 基础字段验证
	const hasBasicFields = (
		typeof question.Id === 'number' &&
		typeof question.session_id === 'string' &&
		typeof question.question_pack_id === 'string'
	);

	// 可选字段验证（这些字段可能不存在或为null）
	const hasValidOptionalFields = (
		(question.question_id === undefined || question.question_id === null || typeof question.question_id === 'number') &&
		(question.question_number === undefined || question.question_number === null || typeof question.question_number === 'number') &&
		(question.question_type === undefined || question.question_type === null || typeof question.question_type === 'string') &&
		(question.points === undefined || question.points === null || typeof question.points === 'number') &&
		(question.prompt === undefined || question.prompt === null || typeof question.prompt === 'string') &&
		(question.options === undefined || question.options === null || typeof question.options === 'string') &&
		(question.correct_answer === undefined || question.correct_answer === null || typeof question.correct_answer === 'string')
	);

	return hasBasicFields && hasValidOptionalFields;
}

// ==================== 数据处理函数 ====================

/**
 * 解析选项字符串为选项数组
 * 支持格式：A. 选项内容\nB. 选项内容\n...
 */
function parseOptionsString(optionsString: string): ParsedOption[] {
	if (!optionsString || typeof optionsString !== 'string') {
		return [];
	}

	// 使用正则表达式匹配选项格式：字母. 内容
	const optionRegex = /([A-Z])\.\s*([^\n\r]+)/g;
	const options: ParsedOption[] = [];
	let match;

	while ((match = optionRegex.exec(optionsString)) !== null) {
		options.push({
			label: match[1],
			content: match[2].trim()
		});
	}

	return options;
}

/**
 * 提取附件中的音频URL
 */
function extractAudioUrls(attachments: AttachmentItem[] | null): string[] {
	if (!attachments || !Array.isArray(attachments)) {
		return [];
	}

	return attachments
		.filter(attachment => attachment.signedUrl)
		.map(attachment => attachment.signedUrl);
}

/**
 * 转换题目数据项为处理后的题目项
 */
function transformQuestionItem(item: QuestionApiItem): ProcessedQuestionItem {
	// 解析选项字符串（处理可能为null或undefined的情况）
	const optionsString = item.options || "";
	const options = parseOptionsString(optionsString);

	// 提取音频URL
	const audioUrls = extractAudioUrls(item.attachment_url || null);

	// 获取主音频URL（第一个音频）
	const primaryAudioUrl = audioUrls.length > 0 ? audioUrls[0] : undefined;

	return {
		id: item.Id,
		questionNumber: item.question_number || 0, // 提供默认值
		questionType: item.question_type || "未知题型", // 提供默认值
		score: item.points || 0, // 提供默认值
		stem: item.prompt || "", // 提供默认值
		options,
		answer: item.correct_answer || "", // 提供默认值
		explanation: item.explanation || "", // 处理null值
		section: item.session_id,
		packageNumber: item.question_pack_id,
		stage: item.stage || undefined, // 处理阶段字段
		audioUrls,
		primaryAudioUrl,
	};
}

// ==================== API 服务类 ====================

/**
 * 题目 API 服务类
 */
export class QuestionApiService {
	/**
	 * 获取题目数据（原始数据）
	 */
	static async getQuestionDataRaw(
		tableId: string,
		sectionName: string,
		packageNumber: string = "1",
		stage?: string
	): Promise<ApiResponse<QuestionApiResponse>> {
		try {
			// 调试：记录API参数
			console.log('[题目API调试] getQuestionDataRaw 参数:', {
				tableId,
				sectionName,
				packageNumber,
				stage,
				hasStage: !!stage,
				timestamp: Date.now()
			});

			// 构建请求端点
			const endpoint = `https://noco.ohvfx.com/api/v2/tables/${tableId}/records`;

			// 构建过滤参数（使用schema定义的英文字段名）
			let whereCondition = `(session_id,eq,${sectionName})~and(question_pack_id,eq,${packageNumber})`;

			// 如果指定了阶段，添加阶段筛选条件
			if (stage) {
				whereCondition += `~and(stage,eq,${stage})`;
				console.log('[题目API调试] 添加了阶段筛选条件:', stage);
			} else {
				console.log('[题目API调试] 没有阶段参数，使用标准筛选');
			}

			console.log('[题目API调试] 最终where条件:', whereCondition);

			const params = {
				where: whereCondition,
				limit: 500, // 设置返回记录数限制，避免数据被分页截断
			};

			// 构建请求头
			const headers = {
				'xc-token': QUESTION_API_CONFIG.token,
			};

			// 发起请求
			const rawResponse = await httpClient.get<any>(
				endpoint,
				params,
				{ headers }
			);

			// 验证原始响应数据格式
			if (!rawResponse.data || !Array.isArray(rawResponse.data.list)) {
				throw createDataFormatError('题目 API 响应数据格式不正确');
			}

			// 原始API响应处理 - 调试日志已移除

			// 使用字段映射适配器转换响应数据
			const mappingResult = FieldMapper.mapArrayFields(
				rawResponse.data.list,
				'question',
				MappingDirection.FROM_API,
				{
					keepUnmappedFields: true,
					applyDefaults: true,
					convertTypes: true,
					debug: true
				}
			);

			// 字段映射处理完成 - 调试日志已移除

			// 应用类型转换
			const typeConversionResult = TypeConverter.convertBatch(
				mappingResult.data,
				'question',
				{ applyDefaults: true, debug: true }
			);

			console.log('[题目API调试] 类型转换结果:', {
				stats: typeConversionResult.stats,
				errors: typeConversionResult.errors,
				warnings: typeConversionResult.warnings
			});

			// 构建最终响应
			const response: ApiResponse<QuestionApiResponse> = {
				data: {
					list: typeConversionResult.data as QuestionApiItem[],
					pageInfo: rawResponse.data.pageInfo
				},
				status: rawResponse.status,
				statusText: rawResponse.statusText,
				headers: rawResponse.headers
			};

			// 验证转换后的数据格式
			if (!validateQuestionApiResponse(response.data)) {
				throw createDataFormatError('题目 API 响应数据格式不正确');
			}

			// 验证列表中每个项目的格式
			for (const item of response.data.list) {
				if (!validateQuestionApiItem(item)) {
					throw createDataFormatError(`题目数据项格式不正确: ${JSON.stringify(item)}`);
				}
			}

			return response;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'QuestionApiService.getQuestionDataRaw');
			throw apiError;
		}
	}

	/**
	 * 获取处理后的题目数据
	 */
	static async getQuestionData(
		tableId: string,
		sectionName: string,
		packageNumber: string = "1",
		stage?: string
	): Promise<ProcessedQuestionItem[]> {
		try {
			// 获取原始数据
			const response = await this.getQuestionDataRaw(tableId, sectionName, packageNumber, stage);

			// 转换数据格式
			const processedQuestions = response.data.list.map(transformQuestionItem);

			// 按题号排序
			processedQuestions.sort((a, b) => a.questionNumber - b.questionNumber);

			return processedQuestions;

		} catch (error) {
			// 记录错误并重新抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'QuestionApiService.getQuestionData');
			throw apiError;
		}
	}

	/**
	 * 检查题目服务是否可用
	 */
	static async checkServiceHealth(
		tableId: string,
		sectionName: string,
		packageNumber: string = "1"
	): Promise<boolean> {
		try {
			// 尝试获取题目数据
			await this.getQuestionDataRaw(tableId, sectionName, packageNumber);
			return true;
		} catch (error) {
			// 记录错误但不抛出
			const apiError = ApiErrorHandler.createApiError(error);
			ApiErrorHandler.logError(apiError, 'QuestionApiService.checkServiceHealth');
			return false;
		}
	}
}

// ==================== 便捷函数导出 ====================

/**
 * 获取题目数据（原始数据）
 */
export const getQuestionDataRaw = QuestionApiService.getQuestionDataRaw.bind(QuestionApiService);

/**
 * 获取处理后的题目数据
 */
export const getQuestionData = QuestionApiService.getQuestionData.bind(QuestionApiService);

/**
 * 检查题目服务健康状态
 */
export const checkQuestionServiceHealth = QuestionApiService.checkServiceHealth.bind(QuestionApiService);

/**
 * 解析选项字符串（导出供其他模块使用）
 */
export { parseOptionsString };

/**
 * 提取音频URL（导出供其他模块使用）
 */
export { extractAudioUrls };
